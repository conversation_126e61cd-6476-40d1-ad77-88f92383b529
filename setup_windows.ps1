# Aider MCP Server - Windows Setup Script
# Dieses Skript richtet die Integration zwischen Aider und Claude Desktop auf Windows ein

param(
    [string]$ProjectPath = $PWD,
    [string]$EditorModel = "gemini/gemini-2.5-pro-exp-03-25",
    [switch]$Force
)

Write-Host "=== Aider MCP Server Windows Setup ===" -ForegroundColor Green
Write-Host "Projekt-Pfad: $ProjectPath" -ForegroundColor Yellow
Write-Host "Editor-Modell: $EditorModel" -ForegroundColor Yellow

# Überprüfe Administrator-Rechte
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "WARNUNG: Dieses Skript sollte als Administrator ausgeführt werden für beste Ergebnisse." -ForegroundColor Yellow
}

# 1. Überprüfe Python Installation
Write-Host "`n1. Überprüfe Python Installation..." -ForegroundColor Cyan
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python gefunden: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "FEHLER: Python ist nicht installiert oder nicht im PATH." -ForegroundColor Red
    Write-Host "Bitte installieren Sie Python von https://python.org" -ForegroundColor Yellow
    exit 1
}

# 2. Überprüfe UV Installation
Write-Host "`n2. Überprüfe UV Installation..." -ForegroundColor Cyan
try {
    $uvVersion = uv --version 2>&1
    Write-Host "UV gefunden: $uvVersion" -ForegroundColor Green
} catch {
    Write-Host "UV nicht gefunden. Installiere UV..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri "https://astral.sh/uv/install.ps1" -OutFile "install_uv.ps1"
        powershell -ExecutionPolicy Bypass -File "install_uv.ps1"
        Remove-Item "install_uv.ps1"
        Write-Host "UV erfolgreich installiert." -ForegroundColor Green
    } catch {
        Write-Host "FEHLER: UV Installation fehlgeschlagen." -ForegroundColor Red
        exit 1
    }
}

# 3. Überprüfe Git Repository
Write-Host "`n3. Überprüfe Git Repository..." -ForegroundColor Cyan
if (-not (Test-Path ".git")) {
    Write-Host "WARNUNG: Kein Git Repository gefunden. Aider funktioniert am besten mit Git." -ForegroundColor Yellow
    $initGit = Read-Host "Möchten Sie ein Git Repository initialisieren? (y/n)"
    if ($initGit -eq "y" -or $initGit -eq "Y") {
        git init
        git add .
        git commit -m "Initial commit for Aider MCP Server"
        Write-Host "Git Repository initialisiert." -ForegroundColor Green
    }
}

# 4. Installiere Abhängigkeiten
Write-Host "`n4. Installiere Projekt-Abhängigkeiten..." -ForegroundColor Cyan
try {
    uv sync
    Write-Host "Abhängigkeiten erfolgreich installiert." -ForegroundColor Green
} catch {
    Write-Host "FEHLER: Installation der Abhängigkeiten fehlgeschlagen." -ForegroundColor Red
    exit 1
}

# 5. Erstelle .env Datei falls nicht vorhanden
Write-Host "`n5. Überprüfe Umgebungsvariablen..." -ForegroundColor Cyan
if (-not (Test-Path ".env")) {
    Write-Host "Erstelle .env Datei..." -ForegroundColor Yellow
    Copy-Item ".env.sample" ".env" -ErrorAction SilentlyContinue
    if (Test-Path ".env") {
        Write-Host ".env Datei erstellt. Bitte fügen Sie Ihre API-Schlüssel hinzu." -ForegroundColor Green
    } else {
        Write-Host "WARNUNG: .env.sample nicht gefunden. Erstelle neue .env Datei..." -ForegroundColor Yellow
        @"
# Aider MCP Server Umgebungsvariablen
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
"@ | Out-File -FilePath ".env" -Encoding UTF8
        Write-Host ".env Datei erstellt." -ForegroundColor Green
    }
}

# 6. Finde Claude Desktop Konfigurationspfad
Write-Host "`n6. Konfiguriere Claude Desktop..." -ForegroundColor Cyan
$claudeConfigPath = "$env:APPDATA\Claude"
$claudeConfigFile = "$claudeConfigPath\claude_desktop_config.json"

# Erstelle Claude Konfigurationsverzeichnis falls nicht vorhanden
if (-not (Test-Path $claudeConfigPath)) {
    New-Item -ItemType Directory -Path $claudeConfigPath -Force
    Write-Host "Claude Konfigurationsverzeichnis erstellt: $claudeConfigPath" -ForegroundColor Green
}

# 7. Erstelle oder aktualisiere Claude Desktop Konfiguration
Write-Host "`n7. Aktualisiere Claude Desktop Konfiguration..." -ForegroundColor Cyan

# Konvertiere Pfade zu Windows-Format
$projectPathWindows = $ProjectPath.Replace("/", "\")
if (-not $projectPathWindows.EndsWith("\")) {
    $projectPathWindows += "\"
}

$claudeConfig = @{
    mcpServers = @{
        "aider-mcp-server" = @{
            command = "uv"
            args = @(
                "--directory",
                $projectPathWindows,
                "run",
                "aider-mcp-server",
                "--editor-model",
                $EditorModel,
                "--current-working-dir",
                $projectPathWindows
            )
            env = @{
                GEMINI_API_KEY = ""
                OPENAI_API_KEY = ""
                ANTHROPIC_API_KEY = ""
            }
        }
    }
}

# Backup existierende Konfiguration
if ((Test-Path $claudeConfigFile) -and (-not $Force)) {
    $backup = "$claudeConfigFile.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $claudeConfigFile $backup
    Write-Host "Backup der existierenden Konfiguration erstellt: $backup" -ForegroundColor Yellow
}

# Schreibe neue Konfiguration
$claudeConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $claudeConfigFile -Encoding UTF8
Write-Host "Claude Desktop Konfiguration aktualisiert: $claudeConfigFile" -ForegroundColor Green

# 8. Teste die Installation
Write-Host "`n8. Teste die Installation..." -ForegroundColor Cyan
try {
    $testResult = uv run aider-mcp-server --help 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Aider MCP Server ist funktionsfähig!" -ForegroundColor Green
    } else {
        Write-Host "WARNUNG: Test fehlgeschlagen. Überprüfen Sie die Konfiguration." -ForegroundColor Yellow
    }
} catch {
    Write-Host "WARNUNG: Test konnte nicht ausgeführt werden." -ForegroundColor Yellow
}

# 9. Abschließende Anweisungen
Write-Host "`n=== Setup abgeschlossen! ===" -ForegroundColor Green
Write-Host "`nNächste Schritte:" -ForegroundColor Yellow
Write-Host "1. Fügen Sie Ihre API-Schlüssel in die .env Datei ein" -ForegroundColor White
Write-Host "2. Starten Sie Claude Desktop neu" -ForegroundColor White
Write-Host "3. Überprüfen Sie in Claude Desktop, ob der MCP Server verbunden ist (Hammer-Symbol)" -ForegroundColor White
Write-Host "`nKonfigurationsdatei: $claudeConfigFile" -ForegroundColor Cyan
Write-Host "Projekt-Pfad: $projectPathWindows" -ForegroundColor Cyan

Write-Host "`nFuer Hilfe und Fehlerbehebung siehe: WINDOWS_SETUP.md" -ForegroundColor Gray
