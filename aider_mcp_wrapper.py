#!/usr/bin/env python3
"""
Aider MCP Server - Windows Wrapper
Dieser Wrapper verbessert die Windows-Kompatibilität und bietet zusätzliche Funktionen.
"""

import os
import sys
import argparse
import asyncio
import logging
import subprocess
from pathlib import Path
from typing import Optional

# Füge das src-Verzeichnis zum Python-Pfad hinzu
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

try:
    from aider_mcp_server.server import serve
    from aider_mcp_server.atoms.utils import DEFAULT_EDITOR_MODEL
except ImportError as e:
    print(f"FEHLER: Kann Aider MCP Server Module nicht importieren: {e}")
    print("<PERSON>ellen <PERSON>e sicher, dass Sie 'uv sync' ausgeführt haben.")
    sys.exit(1)

# Konfiguriere Logging für Windows
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('aider_mcp_server.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def setup_windows_environment():
    """Konfiguriert die Windows-spezifische Umgebung."""
    
    # Lade .env Datei falls vorhanden
    env_file = Path(".env")
    if env_file.exists():
        logger.info("Lade Umgebungsvariablen aus .env Datei...")
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"\'')
                        if value and not value.startswith('your_'):
                            os.environ[key] = value
                            logger.debug(f"Umgebungsvariable gesetzt: {key}")
        except Exception as e:
            logger.warning(f"Fehler beim Laden der .env Datei: {e}")
    
    # Windows-spezifische Pfad-Korrekturen
    if os.name == 'nt':
        # Stelle sicher, dass Git im PATH ist
        git_paths = [
            r"C:\Program Files\Git\bin",
            r"C:\Program Files (x86)\Git\bin",
            os.path.expanduser(r"~\AppData\Local\Programs\Git\bin")
        ]
        
        current_path = os.environ.get('PATH', '')
        for git_path in git_paths:
            if os.path.exists(git_path) and git_path not in current_path:
                os.environ['PATH'] = f"{git_path};{current_path}"
                logger.debug(f"Git-Pfad hinzugefügt: {git_path}")
                break

def validate_working_directory(working_dir: str) -> str:
    """Validiert und normalisiert das Arbeitsverzeichnis für Windows."""
    
    # Konvertiere zu absoluten Pfad
    working_dir = os.path.abspath(working_dir)
    
    # Normalisiere Pfad-Separatoren für Windows
    if os.name == 'nt':
        working_dir = working_dir.replace('/', '\\')
    
    # Überprüfe ob Verzeichnis existiert
    if not os.path.exists(working_dir):
        logger.error(f"Arbeitsverzeichnis existiert nicht: {working_dir}")
        raise FileNotFoundError(f"Arbeitsverzeichnis nicht gefunden: {working_dir}")
    
    # Überprüfe ob es ein Git-Repository ist
    git_dir = os.path.join(working_dir, '.git')
    if not os.path.exists(git_dir):
        logger.warning(f"Kein Git-Repository gefunden in: {working_dir}")
        logger.warning("Aider funktioniert am besten mit Git-Repositories.")
        
        # Frage ob Git-Repository initialisiert werden soll
        if sys.stdin.isatty():  # Nur wenn interaktiv
            response = input("Möchten Sie ein Git-Repository initialisieren? (y/n): ")
            if response.lower() in ['y', 'yes', 'j', 'ja']:
                try:
                    subprocess.run(['git', 'init'], cwd=working_dir, check=True, capture_output=True)
                    subprocess.run(['git', 'add', '.'], cwd=working_dir, check=True, capture_output=True)
                    subprocess.run(['git', 'commit', '-m', 'Initial commit for Aider MCP Server'], 
                                 cwd=working_dir, check=True, capture_output=True)
                    logger.info("Git-Repository erfolgreich initialisiert.")
                except subprocess.CalledProcessError as e:
                    logger.warning(f"Git-Initialisierung fehlgeschlagen: {e}")
    
    return working_dir

def check_api_keys():
    """Überprüft ob mindestens ein API-Schlüssel konfiguriert ist."""
    
    api_keys = {
        'GEMINI_API_KEY': os.environ.get('GEMINI_API_KEY'),
        'OPENAI_API_KEY': os.environ.get('OPENAI_API_KEY'),
        'ANTHROPIC_API_KEY': os.environ.get('ANTHROPIC_API_KEY')
    }
    
    valid_keys = {k: v for k, v in api_keys.items() if v and not v.startswith('your_')}
    
    if not valid_keys:
        logger.error("Keine gültigen API-Schlüssel gefunden!")
        logger.error("Bitte konfigurieren Sie mindestens einen API-Schlüssel in der .env Datei:")
        for key in api_keys.keys():
            logger.error(f"  {key}=your_actual_key_here")
        return False
    
    logger.info(f"Gefundene API-Schlüssel: {', '.join(valid_keys.keys())}")
    return True

def main():
    """Hauptfunktion des Windows-Wrappers."""
    
    parser = argparse.ArgumentParser(
        description="Aider MCP Server - Windows Wrapper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Beispiele:
  python aider_mcp_wrapper.py --current-working-dir "C:\\Users\\<USER>\\MeinProjekt"
  python aider_mcp_wrapper.py --editor-model "openai/gpt-4o" --current-working-dir "."
  python aider_mcp_wrapper.py --help
        """
    )
    
    parser.add_argument(
        "--editor-model",
        type=str,
        default=DEFAULT_EDITOR_MODEL,
        help=f"Editor-Modell zu verwenden (Standard: {DEFAULT_EDITOR_MODEL})"
    )
    
    parser.add_argument(
        "--current-working-dir",
        type=str,
        default=os.getcwd(),
        help="Arbeitsverzeichnis (muss ein gültiges Git-Repository sein)"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help="Logging-Level (Standard: INFO)"
    )
    
    parser.add_argument(
        "--check-only",
        action='store_true',
        help="Nur Konfiguration überprüfen, Server nicht starten"
    )
    
    args = parser.parse_args()
    
    # Setze Logging-Level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    logger.info("=== Aider MCP Server - Windows Wrapper ===")
    logger.info(f"Python Version: {sys.version}")
    logger.info(f"Arbeitsverzeichnis: {args.current_working_dir}")
    logger.info(f"Editor-Modell: {args.editor_model}")
    
    try:
        # 1. Windows-Umgebung einrichten
        logger.info("Richte Windows-Umgebung ein...")
        setup_windows_environment()
        
        # 2. API-Schlüssel überprüfen
        logger.info("Überprüfe API-Schlüssel...")
        if not check_api_keys():
            sys.exit(1)
        
        # 3. Arbeitsverzeichnis validieren
        logger.info("Validiere Arbeitsverzeichnis...")
        working_dir = validate_working_directory(args.current_working_dir)
        
        if args.check_only:
            logger.info("✓ Konfiguration ist gültig!")
            logger.info("Server kann gestartet werden.")
            return
        
        # 4. Server starten
        logger.info("Starte Aider MCP Server...")
        asyncio.run(serve(
            editor_model=args.editor_model,
            current_working_dir=working_dir
        ))
        
    except KeyboardInterrupt:
        logger.info("Server durch Benutzer gestoppt.")
    except Exception as e:
        logger.error(f"Fehler beim Starten des Servers: {e}")
        logger.debug("Vollständiger Fehler:", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
