[project]
name = "aider-mcp-server"
version = "0.1.0"
description = "Model context protocol server for offloading ai coding work to <PERSON><PERSON>"
readme = "README.md"
authors = [
    { name = "IndyDevDan", email = "<EMAIL>" }
]
requires-python = ">=3.12"
dependencies = [
    "aider-chat>=0.81.0",
    "boto3>=1.37.27",
    "mcp>=1.6.0",
    "pydantic>=2.11.2",
    "pytest>=8.3.5",
    "rich>=14.0.0",
]

[project.scripts]
aider-mcp-server = "aider_mcp_server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
