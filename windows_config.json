{"name": "Aider MCP Server - Windows Configuration", "version": "1.0.0", "description": "Windows-spezifische Konfiguration für Aider MCP Server", "windows": {"paths": {"claude_config_dir": "%APPDATA%\\Claude", "claude_config_file": "%APPDATA%\\Claude\\claude_desktop_config.json", "project_root": "C:\\Users\\<USER>\\aider-mcp-server", "default_working_dir": "C:\\Users\\<USER>\\aider-mcp-server"}, "executables": {"uv": "uv.exe", "python": "python.exe", "git": "git.exe"}, "environment": {"encoding": "utf-8", "shell": "cmd", "path_separator": "\\", "line_ending": "\r\n"}}, "claude_desktop": {"mcp_servers": {"aider-mcp-server": {"command": "uv", "args": ["--directory", "C:\\Users\\<USER>\\aider-mcp-server", "run", "aider-mcp-server", "--editor-model", "gemini/gemini-2.5-pro-exp-03-25", "--current-working-dir", "C:\\Users\\<USER>\\aider-mcp-server"], "env": {"GEMINI_API_KEY": "", "OPENAI_API_KEY": "", "ANTHROPIC_API_KEY": "", "AIDER_LOG_LEVEL": "INFO", "MCP_LOG_LEVEL": "INFO"}}}}, "models": {"recommended": [{"name": "gemini/gemini-2.5-pro-exp-03-25", "provider": "Google", "description": "Empfohlenes Modell für beste Leistung", "api_key_env": "GEMINI_API_KEY", "cost": "ni<PERSON><PERSON>"}, {"name": "openai/gpt-4o", "provider": "OpenAI", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von OpenAI", "api_key_env": "OPENAI_API_KEY", "cost": "mittel"}, {"name": "anthropic/claude-3-5-sonnet-20241022", "provider": "Anthropic", "description": "<PERSON> für komplexe Aufgaben", "api_key_env": "ANTHROPIC_API_KEY", "cost": "mittel"}], "alternative": [{"name": "gemini/gemini-2.5-pro-preview-03-25", "provider": "Google", "description": "Preview-Version von Gemini Pro", "api_key_env": "GEMINI_API_KEY"}, {"name": "openrouter/openrouter/quasar-alpha", "provider": "OpenRouter", "description": "Experimentelles Modell über OpenRouter", "api_key_env": "OPENROUTER_API_KEY"}]}, "setup": {"steps": [{"step": 1, "name": "Python Installation überprüfen", "command": "python --version", "required": true}, {"step": 2, "name": "UV Package Manager installieren", "command": "uv --version", "install_command": "powershell -Command \"Invoke-WebRequest -Uri 'https://astral.sh/uv/install.ps1' -OutFile 'install_uv.ps1'; powershell -ExecutionPolicy Bypass -File 'install_uv.ps1'; Remove-Item 'install_uv.ps1'\"", "required": true}, {"step": 3, "name": "Projekt-Abhängigkeiten installieren", "command": "uv sync", "required": true}, {"step": 4, "name": "Umgebungsvariablen konfigurieren", "file": ".env", "template": ".env.windows", "required": true}, {"step": 5, "name": "<PERSON> konfigurieren", "file": "%APPDATA%\\Claude\\claude_desktop_config.json", "required": true}]}, "troubleshooting": {"common_issues": [{"issue": "uv command not found", "solution": "UV ist nicht installiert oder nicht im PATH. Führen Sie install_dependencies.bat aus.", "commands": ["powershell -Command \"Invoke-WebRequest -Uri 'https://astral.sh/uv/install.ps1' -OutFile 'install_uv.ps1'\"", "powershell -ExecutionPolicy Bypass -File 'install_uv.ps1'"]}, {"issue": "python not found", "solution": "Python ist nicht installiert oder nicht im PATH. Installieren Sie Python von python.org.", "url": "https://python.org/downloads/"}, {"issue": "MCP server not connecting", "solution": "Überprüfen Sie die Pfade in claude_desktop_config.json und starten Sie Claude Desktop neu.", "files": ["%APPDATA%\\Claude\\claude_desktop_config.json"]}, {"issue": "API key errors", "solution": "Überprüfen Sie die .env Datei und stellen Sie sicher, dass gültige API-Schlüssel eingetragen sind.", "files": [".env"]}]}, "validation": {"checks": [{"name": "Python verfügbar", "command": "python --version", "expected_pattern": "Python 3\\.(1[2-9]|[2-9]\\d)"}, {"name": "UV verfügbar", "command": "uv --version", "expected_pattern": "uv \\d+\\.\\d+\\.\\d+"}, {"name": "<PERSON>it verfügbar", "command": "git --version", "expected_pattern": "git version \\d+\\.\\d+\\.\\d+"}, {"name": "Projekt-Abhängigkeiten", "file": "uv.lock", "required": true}, {"name": "Umgebungskonfiguration", "file": ".env", "required": true}, {"name": "<PERSON> Konfiguration", "file": "%APPDATA%\\Claude\\claude_desktop_config.json", "required": true}]}}