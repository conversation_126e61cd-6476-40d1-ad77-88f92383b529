#!/usr/bin/env python3
"""
Aider MCP Server - Windows Integration Tests
Testet die Windows-spezifische Funktionalität und Integration.
"""

import os
import sys
import json
import tempfile
import subprocess
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

# Füge das src-Verzeichnis zum Python-Pfad hinzu
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

class TestWindowsIntegration(unittest.TestCase):
    """Tests für Windows-spezifische Integration."""
    
    def setUp(self):
        """Setup für jeden Test."""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
    def tearDown(self):
        """Cleanup nach jedem Test."""
        os.chdir(self.original_cwd)
        
    def test_python_availability(self):
        """Teste ob Python verfügbar ist."""
        try:
            result = subprocess.run(['python', '--version'], 
                                  capture_output=True, text=True, check=True)
            self.assertIn('Python', result.stdout)
            
            # Überprüfe Python-Version (mindestens 3.12)
            version_line = result.stdout.strip()
            version_parts = version_line.split()[1].split('.')
            major, minor = int(version_parts[0]), int(version_parts[1])
            self.assertGreaterEqual(major, 3)
            if major == 3:
                self.assertGreaterEqual(minor, 12)
                
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.fail("Python ist nicht verfügbar oder nicht im PATH")
    
    def test_uv_availability(self):
        """Teste ob UV Package Manager verfügbar ist."""
        try:
            result = subprocess.run(['uv', '--version'], 
                                  capture_output=True, text=True, check=True)
            self.assertIn('uv', result.stdout.lower())
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.skipTest("UV ist nicht installiert - das ist für Tests optional")
    
    def test_git_availability(self):
        """Teste ob Git verfügbar ist."""
        try:
            result = subprocess.run(['git', '--version'], 
                                  capture_output=True, text=True, check=True)
            self.assertIn('git version', result.stdout.lower())
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.skipTest("Git ist nicht installiert - das ist für Tests optional")
    
    def test_windows_path_handling(self):
        """Teste Windows-spezifische Pfad-Behandlung."""
        if os.name != 'nt':
            self.skipTest("Dieser Test ist nur für Windows")
            
        # Teste Pfad-Normalisierung
        test_paths = [
            "C:/Users/<USER>/project",
            "C:\\Users\\<USER>\\project",
            "C:\\Users\\<USER>\\project\\",
            "..\\..\\project"
        ]
        
        for path in test_paths:
            normalized = os.path.abspath(path)
            self.assertTrue(os.path.isabs(normalized))
            self.assertIn('\\', normalized)  # Windows verwendet Backslashes
    
    def test_claude_config_path(self):
        """Teste Claude Desktop Konfigurationspfad."""
        if os.name != 'nt':
            self.skipTest("Dieser Test ist nur für Windows")
            
        expected_path = os.path.expandvars(r"%APPDATA%\Claude")
        self.assertTrue(os.path.isabs(expected_path))
        self.assertIn('AppData', expected_path)
        self.assertIn('Claude', expected_path)
    
    def test_env_file_loading(self):
        """Teste das Laden der .env Datei."""
        # Erstelle Test-.env Datei
        env_content = """
# Test-Umgebungsvariablen
TEST_API_KEY=test_key_123
ANOTHER_VAR=test_value
# Kommentar
EMPTY_VAR=
INVALID_LINE_WITHOUT_EQUALS
"""
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        # Importiere und teste den Wrapper
        try:
            import aider_mcp_wrapper
            
            # Backup aktuelle Umgebung
            original_env = os.environ.copy()
            
            # Teste setup_windows_environment
            aider_mcp_wrapper.setup_windows_environment()
            
            # Überprüfe ob Variablen geladen wurden
            self.assertEqual(os.environ.get('TEST_API_KEY'), 'test_key_123')
            self.assertEqual(os.environ.get('ANOTHER_VAR'), 'test_value')
            self.assertNotIn('EMPTY_VAR', os.environ)
            
            # Restore Umgebung
            os.environ.clear()
            os.environ.update(original_env)
            
        except ImportError:
            self.skipTest("aider_mcp_wrapper kann nicht importiert werden")
    
    def test_claude_config_generation(self):
        """Teste die Generierung der Claude Desktop Konfiguration."""
        project_path = r"C:\Users\<USER>\aider-mcp-server"
        editor_model = "gemini/gemini-2.5-pro-exp-03-25"
        
        expected_config = {
            "mcpServers": {
                "aider-mcp-server": {
                    "command": "uv",
                    "args": [
                        "--directory",
                        project_path,
                        "run",
                        "aider-mcp-server",
                        "--editor-model",
                        editor_model,
                        "--current-working-dir",
                        project_path
                    ],
                    "env": {
                        "GEMINI_API_KEY": "",
                        "OPENAI_API_KEY": "",
                        "ANTHROPIC_API_KEY": ""
                    }
                }
            }
        }
        
        # Teste JSON-Serialisierung
        config_json = json.dumps(expected_config, indent=2)
        self.assertIsInstance(config_json, str)
        
        # Teste JSON-Deserialisierung
        parsed_config = json.loads(config_json)
        self.assertEqual(parsed_config, expected_config)
    
    def test_api_key_validation(self):
        """Teste API-Schlüssel Validierung."""
        try:
            import aider_mcp_wrapper
            
            # Backup aktuelle Umgebung
            original_env = os.environ.copy()
            
            # Test 1: Keine API-Schlüssel
            for key in ['GEMINI_API_KEY', 'OPENAI_API_KEY', 'ANTHROPIC_API_KEY']:
                os.environ.pop(key, None)
            
            self.assertFalse(aider_mcp_wrapper.check_api_keys())
            
            # Test 2: Ungültige API-Schlüssel
            os.environ['GEMINI_API_KEY'] = 'your_gemini_api_key_here'
            self.assertFalse(aider_mcp_wrapper.check_api_keys())
            
            # Test 3: Gültiger API-Schlüssel
            os.environ['GEMINI_API_KEY'] = 'valid_key_123'
            self.assertTrue(aider_mcp_wrapper.check_api_keys())
            
            # Restore Umgebung
            os.environ.clear()
            os.environ.update(original_env)
            
        except ImportError:
            self.skipTest("aider_mcp_wrapper kann nicht importiert werden")
    
    def test_working_directory_validation(self):
        """Teste Arbeitsverzeichnis-Validierung."""
        try:
            import aider_mcp_wrapper
            
            # Test 1: Existierendes Verzeichnis
            valid_dir = aider_mcp_wrapper.validate_working_directory(self.test_dir)
            self.assertEqual(os.path.abspath(valid_dir), os.path.abspath(self.test_dir))
            
            # Test 2: Nicht-existierendes Verzeichnis
            with self.assertRaises(FileNotFoundError):
                aider_mcp_wrapper.validate_working_directory("/non/existent/path")
            
            # Test 3: Relative Pfade
            relative_path = "."
            valid_dir = aider_mcp_wrapper.validate_working_directory(relative_path)
            self.assertTrue(os.path.isabs(valid_dir))
            
        except ImportError:
            self.skipTest("aider_mcp_wrapper kann nicht importiert werden")
    
    @unittest.skipUnless(os.name == 'nt', "Windows-spezifischer Test")
    def test_windows_specific_features(self):
        """Teste Windows-spezifische Features."""
        
        # Teste Windows-Pfad-Separatoren
        test_path = "C:/Users/<USER>/project"
        windows_path = test_path.replace('/', '\\')
        self.assertIn('\\', windows_path)
        self.assertNotIn('/', windows_path)
        
        # Teste Umgebungsvariablen-Expansion
        appdata_path = os.path.expandvars("%APPDATA%")
        self.assertTrue(os.path.isabs(appdata_path))
        self.assertIn("AppData", appdata_path)
        
        # Teste Windows-Kommandos
        try:
            result = subprocess.run(['where', 'python'], 
                                  capture_output=True, text=True, check=True)
            self.assertIn('python', result.stdout.lower())
        except subprocess.CalledProcessError:
            pass  # where-Kommando kann fehlschlagen, das ist ok
    
    def test_config_file_validation(self):
        """Teste Validierung der Konfigurationsdateien."""
        
        # Teste windows_config.json
        config_file = Path("windows_config.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Überprüfe Struktur
            self.assertIn('windows', config)
            self.assertIn('claude_desktop', config)
            self.assertIn('models', config)
            self.assertIn('setup', config)
            
            # Überprüfe Windows-spezifische Pfade
            windows_config = config['windows']
            self.assertIn('paths', windows_config)
            self.assertIn('claude_config_dir', windows_config['paths'])
        
        # Teste claude_desktop_config.json Template
        claude_config_file = Path("claude_desktop_config.json")
        if claude_config_file.exists():
            with open(claude_config_file, 'r', encoding='utf-8') as f:
                claude_config = json.load(f)
            
            # Überprüfe MCP Server Konfiguration
            self.assertIn('mcpServers', claude_config)
            self.assertIn('aider-mcp-server', claude_config['mcpServers'])
            
            server_config = claude_config['mcpServers']['aider-mcp-server']
            self.assertEqual(server_config['command'], 'uv')
            self.assertIn('--directory', server_config['args'])
            self.assertIn('run', server_config['args'])
            self.assertIn('aider-mcp-server', server_config['args'])

class TestWindowsSetupScripts(unittest.TestCase):
    """Tests für Windows Setup-Skripte."""
    
    def test_setup_script_exists(self):
        """Teste ob Setup-Skripte existieren."""
        scripts = [
            'setup_windows.ps1',
            'install_dependencies.bat',
            'start_aider_mcp.bat'
        ]
        
        for script in scripts:
            script_path = Path(script)
            self.assertTrue(script_path.exists(), f"Setup-Skript {script} nicht gefunden")
            
            # Überprüfe ob Datei nicht leer ist
            self.assertGreater(script_path.stat().st_size, 0, f"Setup-Skript {script} ist leer")
    
    def test_documentation_exists(self):
        """Teste ob Windows-Dokumentation existiert."""
        docs = [
            'WINDOWS_SETUP.md',
            'TROUBLESHOOTING_WINDOWS.md'
        ]
        
        for doc in docs:
            doc_path = Path(doc)
            self.assertTrue(doc_path.exists(), f"Dokumentation {doc} nicht gefunden")
            
            # Überprüfe ob Datei nicht leer ist
            self.assertGreater(doc_path.stat().st_size, 0, f"Dokumentation {doc} ist leer")
    
    def test_config_files_exist(self):
        """Teste ob Konfigurationsdateien existieren."""
        configs = [
            'claude_desktop_config.json',
            'windows_config.json',
            '.env.windows'
        ]
        
        for config in configs:
            config_path = Path(config)
            self.assertTrue(config_path.exists(), f"Konfigurationsdatei {config} nicht gefunden")

if __name__ == '__main__':
    # Führe Tests aus
    unittest.main(verbosity=2)
