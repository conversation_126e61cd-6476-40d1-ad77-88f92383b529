# Aider MCP Server - Windows Umgebungsvariablen
# Kopieren Sie diese Datei zu .env und fügen Sie Ihre tatsächlichen API-Schlüssel hinzu

# =============================================================================
# API-Schlüssel (mindestens einer erforderlich)
# =============================================================================

# Gemini API Key (empfohlen für beste Leistung)
# Erhalten Sie Ihren Schlüssel von: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API Key
# Erhalten Sie Ihren Schlüssel von: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key
# Erhalten Sie Ihren Schlüssel von: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# Windows-spezifische Konfiguration
# =============================================================================

# Standard-Editor-Modell für Windows
AIDER_DEFAULT_MODEL=gemini/gemini-2.5-pro-exp-03-25

# Logging-Level (DEBUG, INFO, WARNING, ERROR)
AIDER_LOG_LEVEL=INFO

# Git-Konfiguration
AIDER_AUTO_COMMITS=true
AIDER_USE_GIT=true

# =============================================================================
# Erweiterte Konfiguration (optional)
# =============================================================================

# Claude Desktop Integration
MCP_SERVER_NAME=aider-mcp-server
MCP_LOG_LEVEL=INFO

# Aider-spezifische Einstellungen
AIDER_SUGGEST_SHELL_COMMANDS=false
AIDER_DETECT_URLS=false

# Windows-spezifische Pfade (automatisch erkannt, falls nicht gesetzt)
# AIDER_CHAT_HISTORY_DIR=%USERPROFILE%\.aider
# AIDER_CONFIG_DIR=%USERPROFILE%\.aider

# =============================================================================
# Proxy-Konfiguration (falls erforderlich)
# =============================================================================

# HTTP_PROXY=http://proxy.company.com:8080
# HTTPS_PROXY=http://proxy.company.com:8080
# NO_PROXY=localhost,127.0.0.1

# =============================================================================
# Anweisungen
# =============================================================================

# 1. Kopieren Sie diese Datei zu .env:
#    copy .env.windows .env
#
# 2. Ersetzen Sie "your_*_api_key_here" mit Ihren tatsächlichen API-Schlüsseln
#
# 3. Speichern Sie die Datei
#
# 4. Führen Sie setup_windows.ps1 aus, um die Claude Desktop Integration zu konfigurieren
