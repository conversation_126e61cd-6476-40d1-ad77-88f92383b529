@echo off
REM Aider MCP Server - Windows Dependency Installation Script
REM Dieses Skript installiert alle notwendigen Abhängigkeiten für Windows

echo ===================================
echo Aider MCP Server - Dependency Setup
echo ===================================
echo.

REM Überprüfe Administrator-Rechte
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Administrator-Rechte erkannt.
) else (
    echo [WARNUNG] Dieses Skript sollte als Administrator ausgeführt werden.
    echo Drücken Sie eine beliebige Taste, um fortzufahren...
    pause >nul
)

REM 1. Überprüfe Python Installation
echo [1/5] Überprüfe Python Installation...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Python ist installiert.
    python --version
) else (
    echo [FEHLER] Python ist nicht installiert!
    echo Bitte installieren Sie Python von https://python.org/downloads/
    echo Stellen Sie sicher, dass "Add Python to PATH" aktiviert ist.
    pause
    exit /b 1
)

REM 2. Überprüfe Git Installation
echo.
echo [2/5] Überprüfe Git Installation...
git --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Git ist installiert.
    git --version
) else (
    echo [WARNUNG] Git ist nicht installiert.
    echo Git wird für optimale Aider-Funktionalität empfohlen.
    echo Download: https://git-scm.com/download/win
)

REM 3. Installiere UV Package Manager
echo.
echo [3/5] Installiere UV Package Manager...
uv --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] UV ist bereits installiert.
    uv --version
) else (
    echo [INFO] Installiere UV...
    powershell -Command "Invoke-WebRequest -Uri 'https://astral.sh/uv/install.ps1' -OutFile 'install_uv.ps1'; powershell -ExecutionPolicy Bypass -File 'install_uv.ps1'; Remove-Item 'install_uv.ps1'"
    
    REM Aktualisiere PATH für aktuelle Session
    set "PATH=%PATH%;%USERPROFILE%\.cargo\bin"
    
    uv --version >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] UV erfolgreich installiert.
        uv --version
    ) else (
        echo [FEHLER] UV Installation fehlgeschlagen.
        echo Bitte starten Sie das Terminal neu und versuchen Sie es erneut.
        pause
        exit /b 1
    )
)

REM 4. Installiere Projekt-Abhängigkeiten
echo.
echo [4/5] Installiere Projekt-Abhängigkeiten...
if exist "pyproject.toml" (
    uv sync
    if %errorLevel% == 0 (
        echo [OK] Abhängigkeiten erfolgreich installiert.
    ) else (
        echo [FEHLER] Installation der Abhängigkeiten fehlgeschlagen.
        pause
        exit /b 1
    )
) else (
    echo [WARNUNG] pyproject.toml nicht gefunden. Sind Sie im richtigen Verzeichnis?
    echo Aktuelles Verzeichnis: %CD%
)

REM 5. Erstelle .env Datei
echo.
echo [5/5] Konfiguriere Umgebungsvariablen...
if not exist ".env" (
    if exist ".env.sample" (
        copy ".env.sample" ".env" >nul
        echo [OK] .env Datei aus .env.sample erstellt.
    ) else (
        echo # Aider MCP Server Umgebungsvariablen > .env
        echo GEMINI_API_KEY=your_gemini_api_key_here >> .env
        echo OPENAI_API_KEY=your_openai_api_key_here >> .env
        echo ANTHROPIC_API_KEY=your_anthropic_api_key_here >> .env
        echo [OK] .env Datei erstellt.
    )
    echo [INFO] Bitte fügen Sie Ihre API-Schlüssel in die .env Datei ein.
) else (
    echo [OK] .env Datei existiert bereits.
)

REM Teste Installation
echo.
echo ===================================
echo Installation Test
echo ===================================
echo Teste Aider MCP Server...
uv run aider-mcp-server --help >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Aider MCP Server ist funktionsfähig!
) else (
    echo [WARNUNG] Test fehlgeschlagen. Überprüfen Sie die Konfiguration.
)

echo.
echo ===================================
echo Installation abgeschlossen!
echo ===================================
echo.
echo Nächste Schritte:
echo 1. Bearbeiten Sie die .env Datei und fügen Sie Ihre API-Schlüssel hinzu
echo 2. Führen Sie setup_windows.ps1 aus, um Claude Desktop zu konfigurieren
echo 3. Starten Sie Claude Desktop neu
echo.
echo Für detaillierte Anweisungen siehe: WINDOWS_SETUP.md
echo Für Fehlerbehebung siehe: TROUBLESHOOTING_WINDOWS.md
echo.
pause
