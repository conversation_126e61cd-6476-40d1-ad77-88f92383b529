@echo off
REM Aider MCP Server - Windows Starter Script
REM Einfacher Starter für den Aider MCP Server

title Aider MCP Server

echo ===================================
echo Aider MCP Server - Windows Starter
echo ===================================
echo.

REM Wechsle zum Skript-Verzeichnis
cd /d "%~dp0"

REM Überprüfe ob UV verfügbar ist
uv --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [FEHLER] UV ist nicht installiert oder nicht im PATH.
    echo Bitte führen Sie install_dependencies.bat aus.
    pause
    exit /b 1
)

REM Überprüfe ob .env Datei existiert
if not exist ".env" (
    echo [WARNUNG] .env Datei nicht gefunden.
    if exist ".env.windows" (
        echo Kopiere .env.windows zu .env...
        copy ".env.windows" ".env" >nul
        echo [INFO] Bitte bearbeiten Sie die .env Datei und fügen Sie Ihre API-Schlüssel hinzu.
        echo Drücken Sie eine beliebige Taste, um fortzufahren...
        pause >nul
    ) else (
        echo [FEHLER] Keine Umgebungskonfiguration gefunden.
        echo Bitte führen Sie setup_windows.ps1 aus.
        pause
        exit /b 1
    )
)

REM Standard-Parameter
set "EDITOR_MODEL=gemini/gemini-2.5-pro-exp-03-25"
set "WORKING_DIR=%CD%"

REM Überprüfe Kommandozeilen-Parameter
if "%1"=="--help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="/?" goto :show_help

REM Parse Parameter
:parse_args
if "%1"=="" goto :start_server
if "%1"=="--editor-model" (
    set "EDITOR_MODEL=%2"
    shift
    shift
    goto :parse_args
)
if "%1"=="--working-dir" (
    set "WORKING_DIR=%2"
    shift
    shift
    goto :parse_args
)
if "%1"=="--check" goto :check_only
shift
goto :parse_args

:check_only
echo [INFO] Überprüfe Konfiguration...
python aider_mcp_wrapper.py --check-only --current-working-dir "%WORKING_DIR%" --editor-model "%EDITOR_MODEL%"
pause
exit /b 0

:start_server
echo [INFO] Starte Aider MCP Server...
echo [INFO] Editor-Modell: %EDITOR_MODEL%
echo [INFO] Arbeitsverzeichnis: %WORKING_DIR%
echo [INFO] Drücken Sie Ctrl+C zum Beenden
echo.

REM Starte den Server mit UV
uv run aider-mcp-server --editor-model "%EDITOR_MODEL%" --current-working-dir "%WORKING_DIR%"

if %errorLevel% neq 0 (
    echo.
    echo [FEHLER] Server konnte nicht gestartet werden.
    echo Versuche alternativen Start mit Python-Wrapper...
    python aider_mcp_wrapper.py --editor-model "%EDITOR_MODEL%" --current-working-dir "%WORKING_DIR%"
)

echo.
echo Server beendet.
pause
exit /b 0

:show_help
echo.
echo Verwendung: start_aider_mcp.bat [Optionen]
echo.
echo Optionen:
echo   --editor-model MODEL    Editor-Modell zu verwenden
echo                          (Standard: gemini/gemini-2.5-pro-exp-03-25)
echo   --working-dir DIR       Arbeitsverzeichnis
echo                          (Standard: aktuelles Verzeichnis)
echo   --check                 Nur Konfiguration überprüfen
echo   --help, -h, /?          Diese Hilfe anzeigen
echo.
echo Beispiele:
echo   start_aider_mcp.bat
echo   start_aider_mcp.bat --editor-model "openai/gpt-4o"
echo   start_aider_mcp.bat --working-dir "C:\Users\<USER>\MeinProjekt"
echo   start_aider_mcp.bat --check
echo.
echo Verfügbare Modelle:
echo   gemini/gemini-2.5-pro-exp-03-25     (empfohlen)
echo   openai/gpt-4o
echo   anthropic/claude-3-5-sonnet-20241022
echo.
pause
exit /b 0
