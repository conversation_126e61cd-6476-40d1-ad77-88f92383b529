# Aider MCP Server - Windows Fehlerbehebung

Diese Anleitung hilft bei der Lösung häufiger Probleme mit dem Aider MCP Server auf Windows.

## Allgemeine Diagnose

### 1. Überprüfen Sie die Claude Desktop Logs

1. **Öffnen Sie Claude Desktop**
2. **Aktivieren Sie den Developer Mode:**
   - Menü → Help → Enable Developer Mode
3. **Öffnen Sie die MCP Logs:**
   - Developer → MCP Log File

### 2. Überprüfen Sie die Systemvoraussetzungen

```powershell
# Python Version überprüfen
python --version

# UV Version überprüfen
uv --version

# Git Version überprüfen
git --version

# Aider MCP Server Test
cd C:\Users\<USER>\aider-mcp-server
uv run aider-mcp-server --help
```

## Häufige Probleme und Lösungen

### Problem 1: "uv: command not found"

**Symptome:**
- PowerShell zeigt "uv ist nicht als interner oder externer Befehl erkannt"
- Setup-Skript schlägt fehl

**Lösungen:**

1. **UV manuell installieren:**
   ```powershell
   # UV Installer herunterladen und ausführen
   Invoke-WebRequest -Uri "https://astral.sh/uv/install.ps1" -OutFile "install_uv.ps1"
   powershell -ExecutionPolicy Bypass -File "install_uv.ps1"
   Remove-Item "install_uv.ps1"
   
   # PowerShell neu starten
   ```

2. **PATH Variable überprüfen:**
   ```powershell
   # UV Pfad zum PATH hinzufügen
   $uvPath = "$env:USERPROFILE\.cargo\bin"
   $env:PATH += ";$uvPath"
   
   # Permanent hinzufügen
   [Environment]::SetEnvironmentVariable("PATH", $env:PATH + ";$uvPath", "User")
   ```

### Problem 2: "Python not found"

**Symptome:**
- "python ist nicht als interner oder externer Befehl erkannt"
- UV kann nicht ausgeführt werden

**Lösungen:**

1. **Python installieren:**
   - Laden Sie Python von [python.org](https://python.org/downloads/) herunter
   - **Wichtig:** Aktivieren Sie "Add Python to PATH" während der Installation

2. **Python PATH überprüfen:**
   ```powershell
   # Python Pfad finden
   where python
   
   # Falls nicht gefunden, manuell hinzufügen
   $pythonPath = "C:\Users\<USER>\AppData\Local\Programs\Python\Python312"
   $env:PATH += ";$pythonPath;$pythonPath\Scripts"
   ```

### Problem 3: MCP Server verbindet nicht

**Symptome:**
- Kein Hammer-Symbol in Claude Desktop
- "Server failed to start" in den Logs

**Diagnose:**
```powershell
# Konfigurationsdatei überprüfen
Get-Content "$env:APPDATA\Claude\claude_desktop_config.json"

# Pfade validieren
Test-Path "C:\Users\<USER>\aider-mcp-server"
Test-Path "$env:APPDATA\Claude\claude_desktop_config.json"
```

**Lösungen:**

1. **Pfade korrigieren:**
   ```json
   {
     "mcpServers": {
       "aider-mcp-server": {
         "command": "uv",
         "args": [
           "--directory",
           "C:\\Users\\<USER>\\aider-mcp-server",
           "run",
           "aider-mcp-server",
           "--editor-model",
           "gemini/gemini-2.5-pro-exp-03-25",
           "--current-working-dir",
           "C:\\Users\\<USER>\\aider-mcp-server"
         ]
       }
     }
   }
   ```

2. **Backslashes verdoppeln:**
   - Verwenden Sie `\\` statt `\` in JSON-Pfaden
   - Oder verwenden Sie Forward Slashes: `/`

3. **Claude Desktop vollständig neu starten:**
   ```powershell
   # Alle Claude Prozesse beenden
   Get-Process -Name "*claude*" | Stop-Process -Force
   
   # Claude Desktop neu starten
   Start-Process "C:\Users\<USER>\AppData\Local\Claude\Claude.exe"
   ```

### Problem 4: API-Schlüssel Fehler

**Symptome:**
- "API key not found" oder "Invalid API key"
- Aider kann keine Anfragen stellen

**Lösungen:**

1. **.env Datei überprüfen:**
   ```powershell
   # .env Datei anzeigen
   Get-Content ".env"
   
   # Beispiel-Inhalt:
   # GEMINI_API_KEY=your_actual_key_here
   # OPENAI_API_KEY=your_actual_key_here
   ```

2. **API-Schlüssel validieren:**
   ```powershell
   # Gemini API testen
   $headers = @{ "x-goog-api-key" = "YOUR_GEMINI_KEY" }
   Invoke-RestMethod -Uri "https://generativelanguage.googleapis.com/v1beta/models" -Headers $headers
   ```

3. **Umgebungsvariablen setzen:**
   ```powershell
   # Temporär für aktuelle Session
   $env:GEMINI_API_KEY = "your_actual_key_here"
   
   # Permanent für Benutzer
   [Environment]::SetEnvironmentVariable("GEMINI_API_KEY", "your_key", "User")
   ```

### Problem 5: Git Repository Fehler

**Symptome:**
- "Not a git repository"
- Aider kann keine Commits erstellen

**Lösungen:**

1. **Git Repository initialisieren:**
   ```powershell
   cd C:\Users\<USER>\aider-mcp-server
   git init
   git add .
   git commit -m "Initial commit"
   ```

2. **Git Konfiguration überprüfen:**
   ```powershell
   git config --global user.name "Ihr Name"
   git config --global user.email "<EMAIL>"
   ```

### Problem 6: Berechtigungsfehler

**Symptome:**
- "Access denied" oder "Permission denied"
- Dateien können nicht erstellt/geändert werden

**Lösungen:**

1. **PowerShell als Administrator ausführen:**
   - Rechtsklick auf PowerShell → "Als Administrator ausführen"

2. **Execution Policy anpassen:**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. **Ordner-Berechtigungen überprüfen:**
   ```powershell
   # Vollzugriff für aktuellen Benutzer
   icacls "C:\Users\<USER>\aider-mcp-server" /grant "$env:USERNAME:(OI)(CI)F"
   ```

### Problem 7: Port-Konflikte

**Symptome:**
- "Port already in use"
- Server startet nicht

**Lösungen:**

1. **Verwendete Ports überprüfen:**
   ```powershell
   netstat -ano | findstr :8283
   ```

2. **Prozesse beenden:**
   ```powershell
   # Prozess-ID finden und beenden
   Get-Process -Id <PID> | Stop-Process -Force
   ```

## Erweiterte Diagnose

### Debug-Modus aktivieren

1. **Logging in Claude Desktop Konfiguration:**
   ```json
   {
     "mcpServers": {
       "aider-mcp-server": {
         "command": "uv",
         "args": [...],
         "env": {
           "AIDER_LOG_LEVEL": "DEBUG",
           "MCP_LOG_LEVEL": "DEBUG"
         }
       }
     }
   }
   ```

2. **Verbose Ausgabe:**
   ```powershell
   uv run aider-mcp-server --help --verbose
   ```

### Konfiguration validieren

```powershell
# JSON Syntax überprüfen
Get-Content "$env:APPDATA\Claude\claude_desktop_config.json" | ConvertFrom-Json

# Pfade validieren
$config = Get-Content "$env:APPDATA\Claude\claude_desktop_config.json" | ConvertFrom-Json
$projectPath = $config.mcpServers."aider-mcp-server".args[1]
Test-Path $projectPath
```

### Netzwerk-Diagnose

```powershell
# Internet-Verbindung testen
Test-NetConnection -ComputerName "api.openai.com" -Port 443
Test-NetConnection -ComputerName "generativelanguage.googleapis.com" -Port 443

# DNS auflösen
Resolve-DnsName "api.openai.com"
```

## Komplette Neuinstallation

Falls alle anderen Lösungen fehlschlagen:

```powershell
# 1. Alle Claude Prozesse beenden
Get-Process -Name "*claude*" | Stop-Process -Force

# 2. Claude Konfiguration löschen
Remove-Item "$env:APPDATA\Claude" -Recurse -Force

# 3. Projekt-Verzeichnis säubern
cd C:\Users\<USER>\aider-mcp-server
Remove-Item ".venv" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item "uv.lock" -Force -ErrorAction SilentlyContinue

# 4. Neuinstallation
uv sync
.\setup_windows.ps1 -Force
```

## Support und weitere Hilfe

### Log-Dateien sammeln

```powershell
# System-Informationen sammeln
$logPath = "aider_mcp_debug_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"

@"
=== Aider MCP Server Debug Information ===
Datum: $(Get-Date)
Windows Version: $(Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion)
PowerShell Version: $($PSVersionTable.PSVersion)

=== Python Information ===
$(python --version 2>&1)
$(python -c "import sys; print('Python Path:', sys.executable)" 2>&1)

=== UV Information ===
$(uv --version 2>&1)

=== Git Information ===
$(git --version 2>&1)

=== Claude Configuration ===
$(Get-Content "$env:APPDATA\Claude\claude_desktop_config.json" -ErrorAction SilentlyContinue)

=== Environment Variables ===
GEMINI_API_KEY: $(if($env:GEMINI_API_KEY) { "SET (length: $($env:GEMINI_API_KEY.Length))" } else { "NOT SET" })
OPENAI_API_KEY: $(if($env:OPENAI_API_KEY) { "SET (length: $($env:OPENAI_API_KEY.Length))" } else { "NOT SET" })

=== Project Structure ===
$(Get-ChildItem -Recurse -Depth 2 | Select-Object Name, FullName)
"@ | Out-File -FilePath $logPath -Encoding UTF8

Write-Host "Debug-Informationen gespeichert in: $logPath"
```

### Kontakt

- **GitHub Issues:** [aider-mcp-server Issues](https://github.com/disler/aider-mcp-server/issues)
- **Aider Community:** [aider.chat](https://aider.chat/)
- **Claude Support:** [Claude Help Center](https://support.anthropic.com/)
