# Aider MCP Server - Windows Setup Anleitung

Diese Anleitung erklärt, wie Sie den Aider MCP Server mit der Claude Desktop App auf Windows einrichten.

## Voraussetzungen

### 1. Software-Anforderungen
- **Windows 10/11** (64-bit)
- **Python 3.12+** ([Download](https://python.org/downloads/))
- **Git** ([Download](https://git-scm.com/download/win))
- **<PERSON>op App** ([Download](https://claude.ai/download))
- **UV Package Manager** (wird automatisch installiert)

### 2. API-Schlüssel
Sie benötigen mindestens einen der folgenden API-Schlüssel:
- **Gemini API Key** (empfohlen) - [Google AI Studio](https://makersuite.google.com/app/apikey)
- **OpenAI API Key** - [OpenAI Platform](https://platform.openai.com/api-keys)
- **Anthropic API Key** - [Anthropic Console](https://console.anthropic.com/)

## Automatische Installation

### Option 1: PowerShell Setup-Skript (Empfohlen)

1. **Öffnen Sie PowerShell als Administrator:**
   - Drücken Sie `Windows + X`
   - Wählen Sie "Windows PowerShell (Administrator)"

2. **Navigieren Sie zum Projekt-Verzeichnis:**
   ```powershell
   cd C:\Users\<USER>\aider-mcp-server
   ```

3. **Führen Sie das Setup-Skript aus:**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   .\setup_windows.ps1
   ```

4. **Folgen Sie den Anweisungen des Skripts**

Das Skript wird automatisch:
- Python und UV überprüfen/installieren
- Projekt-Abhängigkeiten installieren
- Git Repository initialisieren (falls nötig)
- Claude Desktop Konfiguration erstellen
- Installation testen

## Manuelle Installation

### Schritt 1: Repository klonen und Abhängigkeiten installieren

```powershell
# Repository klonen
git clone https://github.com/disler/aider-mcp-server.git
cd aider-mcp-server

# UV installieren (falls nicht vorhanden)
Invoke-WebRequest -Uri "https://astral.sh/uv/install.ps1" -OutFile "install_uv.ps1"
powershell -ExecutionPolicy Bypass -File "install_uv.ps1"

# Abhängigkeiten installieren
uv sync
```

### Schritt 2: Umgebungsvariablen konfigurieren

1. **Kopieren Sie die Beispiel-Konfiguration:**
   ```powershell
   copy .env.sample .env
   ```

2. **Bearbeiten Sie die .env Datei:**
   ```
   GEMINI_API_KEY=your_actual_gemini_api_key_here
   OPENAI_API_KEY=your_actual_openai_api_key_here
   ANTHROPIC_API_KEY=your_actual_anthropic_api_key_here
   ```

### Schritt 3: Claude Desktop konfigurieren

1. **Öffnen Sie den Claude Konfigurationsordner:**
   - Drücken Sie `Windows + R`
   - Geben Sie ein: `%APPDATA%\Claude`
   - Drücken Sie Enter

2. **Erstellen/Bearbeiten Sie `claude_desktop_config.json`:**
   ```json
   {
     "mcpServers": {
       "aider-mcp-server": {
         "command": "uv",
         "args": [
           "--directory",
           "C:\\Users\\<USER>\\aider-mcp-server",
           "run",
           "aider-mcp-server",
           "--editor-model",
           "gemini/gemini-2.5-pro-exp-03-25",
           "--current-working-dir",
           "C:\\Users\\<USER>\\aider-mcp-server"
         ],
         "env": {
           "GEMINI_API_KEY": "",
           "OPENAI_API_KEY": "",
           "ANTHROPIC_API_KEY": ""
         }
       }
     }
   }
   ```

   **Wichtig:** Ersetzen Sie `C:\\Users\\<USER>\\aider-mcp-server` mit Ihrem tatsächlichen Projektpfad.

### Schritt 4: Claude Desktop neu starten

1. **Schließen Sie Claude Desktop vollständig:**
   - Rechtsklick auf das Claude-Symbol in der Taskleiste
   - Wählen Sie "Beenden"
   - Oder verwenden Sie den Task-Manager

2. **Starten Sie Claude Desktop neu**

3. **Überprüfen Sie die Verbindung:**
   - Schauen Sie nach dem Hammer-Symbol (🔨) in Claude Desktop
   - Dies zeigt an, dass MCP Server verbunden sind

## Konfigurationsoptionen

### Editor-Modelle

Sie können verschiedene AI-Modelle verwenden:

```json
"--editor-model", "gemini/gemini-2.5-pro-exp-03-25"     // Gemini (empfohlen)
"--editor-model", "openai/gpt-4o"                       // OpenAI GPT-4o
"--editor-model", "anthropic/claude-3-5-sonnet-20241022" // Claude Sonnet
```

### Arbeitsverzeichnis anpassen

Ändern Sie das `--current-working-dir` Argument auf Ihr gewünschtes Projektverzeichnis:

```json
"--current-working-dir", "C:\\Users\\<USER>\\MeinProjekt"
```

## Testen der Installation

### 1. Kommandozeilen-Test
```powershell
cd C:\Users\<USER>\aider-mcp-server
uv run aider-mcp-server --help
```

### 2. Claude Desktop Test
1. Öffnen Sie Claude Desktop
2. Starten Sie eine neue Unterhaltung
3. Schauen Sie nach dem Hammer-Symbol (🔨)
4. Testen Sie mit einem einfachen Prompt:
   ```
   Verwende das aider_ai_code Tool, um eine einfache "Hello World" Python-Datei zu erstellen.
   ```

## Fehlerbehebung

Siehe [TROUBLESHOOTING_WINDOWS.md](TROUBLESHOOTING_WINDOWS.md) für detaillierte Fehlerbehebung.

### Häufige Probleme

1. **"uv: command not found"**
   - UV ist nicht installiert oder nicht im PATH
   - Lösung: Führen Sie das Setup-Skript erneut aus

2. **"Python not found"**
   - Python ist nicht installiert oder nicht im PATH
   - Lösung: Installieren Sie Python von python.org

3. **MCP Server verbindet nicht**
   - Überprüfen Sie die Pfade in der Konfiguration
   - Stellen Sie sicher, dass alle Backslashes doppelt sind (`\\`)
   - Starten Sie Claude Desktop neu

4. **API-Schlüssel Fehler**
   - Überprüfen Sie die .env Datei
   - Stellen Sie sicher, dass die API-Schlüssel gültig sind

## Erweiterte Konfiguration

### Mehrere Projekte

Sie können mehrere Aider MCP Server für verschiedene Projekte konfigurieren:

```json
{
  "mcpServers": {
    "aider-project-1": {
      "command": "uv",
      "args": [
        "--directory", "C:\\Users\\<USER>\\aider-mcp-server",
        "run", "aider-mcp-server",
        "--editor-model", "gemini/gemini-2.5-pro-exp-03-25",
        "--current-working-dir", "C:\\Users\\<USER>\\projekt1"
      ]
    },
    "aider-project-2": {
      "command": "uv",
      "args": [
        "--directory", "C:\\Users\\<USER>\\aider-mcp-server",
        "run", "aider-mcp-server",
        "--editor-model", "openai/gpt-4o",
        "--current-working-dir", "C:\\Users\\<USER>\\projekt2"
      ]
    }
  }
}
```

### Logging aktivieren

Für erweiterte Debugging-Informationen können Sie Logging aktivieren:

```json
"env": {
  "AIDER_LOG_LEVEL": "DEBUG",
  "GEMINI_API_KEY": "your_key_here"
}
```

## Support

- **GitHub Issues:** [aider-mcp-server Issues](https://github.com/disler/aider-mcp-server/issues)
- **Dokumentation:** [README.md](README.md)
- **Aider Dokumentation:** [aider.chat](https://aider.chat/)
